import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:octasync_client/components/selector/department_selector/department_tree.dart';
import 'package:octasync_client/components/selector/department_selector/department_selector_provider.dart';
import 'package:octasync_client/imports.dart';

/// 部门选择对话框内容
class DepartmentSelectorDialog extends StatelessWidget {
  final GlobalKey<DepartmentTreeState> departmentSelectorKey;

  const DepartmentSelectorDialog({super.key, required this.departmentSelectorKey});

  /// 更新选中的部门列表
  void _updateCheckedDepartments(BuildContext context) {
    final checkedDepartments = departmentSelectorKey.currentState?.getAllCheckedDepartments() ?? [];
    context.read<DepartmentSelectorProvider>().updateCheckedDepartments(checkedDepartments);
  }

  @override
  Widget build(BuildContext context) {
    // 重置状态到默认选中
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<DepartmentSelectorProvider>().resetToDefault();
    });

    return Row(
      children: [
        // 左侧部门树状
        Expanded(
          child: Container(
            width: 250,
            decoration: BoxDecoration(border: Border(right: BorderSide(color: context.border300))),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    border: Border(bottom: BorderSide(color: context.border300)),
                  ),
                  child: AppInput(
                    hintText: '搜索',
                    size: InputSize.medium,
                    showErrMsg: false,
                    onChanged: (value) {
                      departmentSelectorKey.currentState?.setSearchQuery(value);
                      context.read<DepartmentSelectorProvider>().setSearchQuery(value);
                    },
                  ),
                ),
                Expanded(
                  child: DepartmentTree(
                    key: departmentSelectorKey,
                    showCheckbox: true,
                    onNodeSelected: (department, value) {
                      _updateCheckedDepartments(context);
                    },
                    onDataLoaded: () {
                      // 数据加载完成后应用默认选中状态
                      context.read<DepartmentSelectorProvider>().applyDefaultCheckedState(
                        departmentSelectorKey,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
        // 右侧已选列表
        Expanded(
          child: Consumer<DepartmentSelectorProvider>(
            builder: (context, provider, child) {
              final checkedDepartments = provider.checkedDepartments;
              return Padding(
                padding: const EdgeInsets.all(10),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('已选择(${provider.checkedCount})'),
                        AppButton(
                          text: '清空全部',
                          type: ButtonType.primary,
                          textOnly: true,
                          onPressed: () {
                            departmentSelectorKey.currentState?.resetAllNodesCheck();
                            provider.clearAllCheckedDepartments();
                          },
                        ),
                      ],
                    ),
                    Divider(color: context.border300),
                    Expanded(
                      child: ListView.builder(
                        itemCount: checkedDepartments.length,
                        itemBuilder: (context, index) {
                          final department = checkedDepartments[index];
                          return Padding(
                            padding: const EdgeInsets.only(top: 5, bottom: 5, right: 10),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        department.departmentName,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      if (department.parentName!.isNotEmpty)
                                        Text(
                                          department.parentName ?? '',
                                          style: TextStyle(color: AppColors.textHint, fontSize: 12),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                    ],
                                  ),
                                ),
                                MouseRegion(
                                  cursor: SystemMouseCursors.click,
                                  child: GestureDetector(
                                    onTap: () {
                                      departmentSelectorKey.currentState?.uncheckNode(
                                        department.id!,
                                      );
                                      provider.removeCheckedDepartment(department.id!);
                                    },
                                    child: Icon(
                                      IconFont.mianxing_jianshao,
                                      color: AppColors.error,
                                      size: AppIconSize.medium,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
