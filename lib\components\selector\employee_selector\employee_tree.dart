import 'package:flutter/material.dart';
import 'package:octasync_client/api/employee.dart';
import 'package:octasync_client/components/selector/employee_selector/employee_tree_model.dart';
import 'package:octasync_client/imports.dart';

// 常量定义
class _EmployeeTreeConstants {
  static const String loadingMessage = '正在加载人员数据...';
  static const String emptyDataMessage = '暂无人员数据';
  static const String retryButtonText = '重试';
  static const String loadErrorPrefix = '加载人员数据失败: ';
}

/// 人员树节点数据模型 - 包装 EmployeeTreeModel 并添加UI状态
class EmployeeTreeNode {
  final EmployeeTreeModel employee;
  final List<EmployeeTreeNode> children;
  bool isExpanded;
  bool isSelected; // 节点点击选择状态（高亮显示）
  bool isChecked; // 复选框选中状态
  bool isIndeterminate; // 复选框半选状态
  bool isVisible; // 搜索时节点是否可见

  // 缓存计算结果以提高性能
  int? _cachedLevel;
  int? _cachedCheckedCount;

  EmployeeTreeNode({
    required this.employee,
    List<EmployeeTreeNode>? children,
    this.isExpanded = false,
    this.isSelected = false,
    this.isChecked = false,
    this.isIndeterminate = false,
    this.isVisible = true,
  }) : children = children ?? [];

  /// 检查是否为叶子节点
  bool get isLeaf => children.isEmpty;

  /// 获取节点层级深度（带缓存）
  int get level {
    _cachedLevel ??= _calculateLevel();
    return _cachedLevel!;
  }

  int _calculateLevel() {
    if (employee.parentId == null) return 0;
    // 简单计算层级，可以根据需要优化
    return employee.isDepartment ? 0 : 1;
  }

  /// 获取所有被选中的子节点数量（带缓存）
  int get checkedChildrenCount {
    _cachedCheckedCount ??= children.where((child) => child.isChecked).length;
    return _cachedCheckedCount!;
  }

  /// 清除缓存（当子节点状态变化时调用）
  void clearCache() {
    _cachedCheckedCount = null;
  }

  /// 获取所有子节点数量
  int get totalChildrenCount => children.length;

  /// 检查是否有部分子节点被选中
  bool get hasPartiallyCheckedChildren {
    if (children.isEmpty) return false;
    final checkedCount = checkedChildrenCount;
    return checkedCount > 0 && checkedCount < totalChildrenCount;
  }

  /// 检查是否所有子节点都被选中
  bool get hasAllChildrenChecked {
    if (children.isEmpty) return false;
    return checkedChildrenCount == totalChildrenCount;
  }

  /// 复制节点状态（用于状态管理）
  void copyStateFrom(EmployeeTreeNode other) {
    isExpanded = other.isExpanded;
    isSelected = other.isSelected;
    isChecked = other.isChecked;
    isIndeterminate = other.isIndeterminate;
    isVisible = other.isVisible;
    clearCache();
  }
}

/// 人员树状选择器组件
class EmployeeTree extends StatefulWidget {
  /// 是否显示复选框
  final bool showCheckbox;

  /// 节点点击回调
  final void Function(EmployeeTreeModel employee)? onNodeTap;

  /// 节点选择回调（复选框点击时触发）
  final void Function(EmployeeTreeModel employee, bool isSelected)? onNodeSelected;

  /// 搜索查询字符串
  final String? searchQuery;

  /// 数据加载完成回调
  final VoidCallback? onDataLoaded;

  const EmployeeTree({
    super.key,
    this.showCheckbox = false,
    this.onNodeTap,
    this.onNodeSelected,
    this.searchQuery,
    this.onDataLoaded,
  });

  @override
  State<EmployeeTree> createState() => EmployeeTreeState();
}

// 将 State 类改为公开，以便外部可以访问
class EmployeeTreeState extends State<EmployeeTree> {
  List<EmployeeTreeModel> _list = [];
  final List<EmployeeTreeNode> _treeNodes = [];
  String? _currentSearchQuery;

  // 性能优化：缓存节点映射
  final Map<String, EmployeeTreeNode> _nodeMap = {};

  // 加载状态管理
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _currentSearchQuery = widget.searchQuery;
    _loadEmployeeData();
  }

  @override
  void didUpdateWidget(EmployeeTree oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当搜索查询发生变化时，更新搜索结果
    if (widget.searchQuery != _currentSearchQuery) {
      _currentSearchQuery = widget.searchQuery;
      _applySearchFilter();
    }
  }

  @override
  void dispose() {
    // 清理资源
    _nodeMap.clear();
    super.dispose();
  }

  /// 刷新人员列表数据 - 公开方法供外部调用
  void refresh() {
    _loadEmployeeData();
  }

  /// 展开所有顶级部门节点 - 公开方法供外部调用
  void expandTopLevelDepartments() {
    _expandTopLevelDepartments();
    setState(() {});
  }

  /// 展开指定节点
  void expandNode(String nodeId) {
    if (_nodeMap.containsKey(nodeId)) {
      _nodeMap[nodeId]!.isExpanded = true;
      setState(() {});
    }
  }

  /// 收起指定节点
  void collapseNode(String nodeId) {
    if (_nodeMap.containsKey(nodeId)) {
      _nodeMap[nodeId]!.isExpanded = false;
      setState(() {});
    }
  }

  /// 获取演示数据
  List<Map<String, dynamic>> _getDemoData() {
    return [
      {"Id": "1000", "Name": "某某公司", "ParentId": null, "ParentName": "", "Type": 1},
      {"Id": "1001", "Name": "总经理办公室", "ParentId": "1000", "ParentName": "某某公司", "Type": 1},
      {"Id": "1002", "Name": "技术部", "ParentId": "1000", "ParentName": "某某公司", "Type": 1},
      {"Id": "1003", "Name": "人力资源部", "ParentId": "1000", "ParentName": "某某公司", "Type": 1},
      {"Id": "1004", "Name": "财务部", "ParentId": "1000", "ParentName": "某某公司", "Type": 1},
      {"Id": "1005", "Name": "前端开发组", "ParentId": "1002", "ParentName": "技术部", "Type": 1},
      {"Id": "1006", "Name": "后端开发组", "ParentId": "1002", "ParentName": "技术部", "Type": 1},
      {"Id": "1007", "Name": "测试组", "ParentId": "1002", "ParentName": "技术部", "Type": 1},
      {"Id": "1008", "Name": "招聘组", "ParentId": "1003", "ParentName": "人力资源部", "Type": 1},
      {"Id": "1009", "Name": "薪酬福利组", "ParentId": "1003", "ParentName": "人力资源部", "Type": 1},
      {"Id": "1010", "Name": "会计组", "ParentId": "1004", "ParentName": "财务部", "Type": 1},
      {"Id": "2001", "Name": "张总", "ParentId": "1001", "ParentName": "总经理办公室", "Type": 2},
      {"Id": "2002", "Name": "李秘书", "ParentId": "1001", "ParentName": "总经理办公室", "Type": 2},
      {"Id": "2003", "Name": "王小明", "ParentId": "1005", "ParentName": "前端开发组", "Type": 2},
      {"Id": "2004", "Name": "陈小红", "ParentId": "1005", "ParentName": "前端开发组", "Type": 2},
      {"Id": "2005", "Name": "刘大伟", "ParentId": "1006", "ParentName": "后端开发组", "Type": 2},
      {"Id": "2006", "Name": "赵小丽", "ParentId": "1006", "ParentName": "后端开发组", "Type": 2},
      {"Id": "2007", "Name": "孙测试", "ParentId": "1007", "ParentName": "测试组", "Type": 2},
      {"Id": "2008", "Name": "周小华", "ParentId": "1008", "ParentName": "招聘组", "Type": 2},
      {"Id": "2009", "Name": "吴小芳", "ParentId": "1009", "ParentName": "薪酬福利组", "Type": 2},
      {"Id": "2010", "Name": "郑会计", "ParentId": "1010", "ParentName": "会计组", "Type": 2},
    ];
  }

  /// 加载人员数据
  Future<void> _loadEmployeeData() async {
    if (_isLoading) return; // 防止重复加载

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // final res = await EmployeeApi.getTreeList();
      final res = _getDemoData();
      if (mounted) {
        setState(() {
          _list = EmployeeTreeModel.fromJsonList(res);
          _buildTreeStructure();
          _applySearchFilter(); // 构建树结构后应用搜索过滤
          _isLoading = false;
        });
        // 数据加载完成后调用回调
        widget.onDataLoaded?.call();
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _errorMessage = '${_EmployeeTreeConstants.loadErrorPrefix}$error';
          _isLoading = false;
        });
      }
    }
  }

  /// 构建树结构
  void _buildTreeStructure() {
    _nodeMap.clear();
    _treeNodes.clear();

    // 从扁平数据构建树形结构
    _buildTreeFromFlatData();
    // 默认展开最顶级的部门节点
    _expandTopLevelDepartments();
  }

  /// 从扁平数据构建树形结构
  void _buildTreeFromFlatData() {
    // 创建所有节点的映射
    for (final employee in _list) {
      _nodeMap[employee.id!] = EmployeeTreeNode(employee: employee);
    }

    // 构建父子关系
    for (final node in _nodeMap.values) {
      if (node.employee.parentId != null && _nodeMap.containsKey(node.employee.parentId)) {
        final parent = _nodeMap[node.employee.parentId!]!;
        parent.children.add(node);
      } else {
        // 顶级节点（部门）
        if (node.employee.isDepartment) {
          _treeNodes.add(node);
        }
      }
    }

    // 对子节点进行排序：部门在前，人员在后
    for (final node in _nodeMap.values) {
      node.children.sort((a, b) {
        if (a.employee.isDepartment && !b.employee.isDepartment) return -1;
        if (!a.employee.isDepartment && b.employee.isDepartment) return 1;
        return a.employee.name.compareTo(b.employee.name);
      });
    }

    // 对顶级节点排序
    _treeNodes.sort((a, b) => a.employee.name.compareTo(b.employee.name));
  }

  /// 默认展开最顶级的部门节点
  void _expandTopLevelDepartments() {
    for (final node in _treeNodes) {
      if (node.employee.isDepartment) {
        node.isExpanded = true;
      }
    }
  }

  /// 应用搜索过滤
  void _applySearchFilter() {
    if (_currentSearchQuery == null || _currentSearchQuery!.isEmpty) {
      // 如果没有搜索查询，显示所有节点并重新展开顶级部门
      _setAllNodesVisible(true);
      _expandTopLevelDepartments();
      setState(() {});
      return;
    }

    final query = _currentSearchQuery!.toLowerCase();
    _setAllNodesVisible(false);

    // 标记匹配的节点为可见，并展开其祖先节点
    for (final node in _nodeMap.values) {
      if (node.employee.name.toLowerCase().contains(query)) {
        _setNodeAndAncestorsVisible(node);
        _expandAncestorNodes(node);
      }
    }

    setState(() {});
  }

  /// 设置所有节点的可见性
  void _setAllNodesVisible(bool visible) {
    for (final node in _nodeMap.values) {
      node.isVisible = visible;
    }
  }

  /// 设置节点及其祖先节点为可见
  void _setNodeAndAncestorsVisible(EmployeeTreeNode node) {
    node.isVisible = true;
    if (node.employee.parentId != null && _nodeMap.containsKey(node.employee.parentId)) {
      _setNodeAndAncestorsVisible(_nodeMap[node.employee.parentId!]!);
    }
  }

  /// 展开节点的所有祖先节点（用于搜索时显示匹配结果）
  void _expandAncestorNodes(EmployeeTreeNode node) {
    if (node.employee.parentId != null && _nodeMap.containsKey(node.employee.parentId)) {
      final parent = _nodeMap[node.employee.parentId!]!;
      parent.isExpanded = true;
      _expandAncestorNodes(parent);
    }
  }

  /// 设置搜索查询
  void setSearchQuery(String query) {
    // 处理空白字符，将其视为空搜索
    _currentSearchQuery = query.trim().isEmpty ? null : query.trim();
    _applySearchFilter();
  }

  /// 获取所有选中的人员
  List<EmployeeTreeModel> getAllCheckedEmployees() {
    final List<EmployeeTreeModel> checkedEmployees = [];
    for (final node in _nodeMap.values) {
      if (node.isChecked && node.employee.isEmployee) {
        checkedEmployees.add(node.employee);
      }
    }
    return checkedEmployees;
  }

  /// 选中节点
  void checkNode(String nodeId) {
    if (_nodeMap.containsKey(nodeId)) {
      _setNodeChecked(_nodeMap[nodeId]!, true);
    }
  }

  /// 取消选中节点
  void uncheckNode(String nodeId) {
    if (_nodeMap.containsKey(nodeId)) {
      _setNodeChecked(_nodeMap[nodeId]!, false);
    }
  }

  /// 重置所有节点的选中状态
  void resetAllNodesCheck() {
    for (final node in _nodeMap.values) {
      node.isChecked = false;
      node.isIndeterminate = false;
      node.clearCache();
    }
    setState(() {});
  }

  /// 设置节点选中状态
  void _setNodeChecked(EmployeeTreeNode node, bool checked) {
    node.isChecked = checked;
    node.isIndeterminate = false;

    // 如果是部门节点，递归选中/取消选中所有子节点（包括子部门和人员）
    if (node.employee.isDepartment) {
      _setAllChildrenChecked(node, checked);
    }

    // 更新父节点状态
    _updateParentNodeState(node);

    setState(() {});

    // 触发选择回调
    widget.onNodeSelected?.call(node.employee, checked);
  }

  /// 递归设置所有子节点的选中状态
  void _setAllChildrenChecked(EmployeeTreeNode parentNode, bool checked) {
    for (final child in parentNode.children) {
      child.isChecked = checked;
      child.isIndeterminate = false;
      child.clearCache();

      // 如果子节点也是部门，递归处理其子节点
      if (child.employee.isDepartment) {
        _setAllChildrenChecked(child, checked);
      }
    }
  }

  /// 更新父节点状态
  void _updateParentNodeState(EmployeeTreeNode node) {
    if (node.employee.parentId != null && _nodeMap.containsKey(node.employee.parentId)) {
      final parent = _nodeMap[node.employee.parentId!]!;
      parent.clearCache();

      // 计算父节点的选中状态
      _calculateParentCheckState(parent);

      // 递归更新上级父节点
      _updateParentNodeState(parent);
    }
  }

  /// 计算父节点的选中状态
  void _calculateParentCheckState(EmployeeTreeNode parentNode) {
    if (parentNode.children.isEmpty) {
      parentNode.isChecked = false;
      parentNode.isIndeterminate = false;
      return;
    }

    int checkedCount = 0;
    int indeterminateCount = 0;
    int totalCount = parentNode.children.length;

    for (final child in parentNode.children) {
      if (child.isChecked) {
        checkedCount++;
      } else if (child.isIndeterminate) {
        indeterminateCount++;
      }
    }

    // 确定父节点的状态
    if (checkedCount == totalCount) {
      // 所有子节点都被选中
      parentNode.isChecked = true;
      parentNode.isIndeterminate = false;
    } else if (checkedCount == 0 && indeterminateCount == 0) {
      // 没有子节点被选中或处于半选状态
      parentNode.isChecked = false;
      parentNode.isIndeterminate = false;
    } else {
      // 部分子节点被选中或有半选状态
      parentNode.isChecked = false;
      parentNode.isIndeterminate = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(_EmployeeTreeConstants.loadingMessage),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(_errorMessage!),
            const SizedBox(height: 16),
            AppButton(text: _EmployeeTreeConstants.retryButtonText, onPressed: _loadEmployeeData),
          ],
        ),
      );
    }

    if (_treeNodes.isEmpty) {
      return const Center(child: Text(_EmployeeTreeConstants.emptyDataMessage));
    }

    return ListView.builder(
      itemCount: _treeNodes.length,
      itemBuilder: (context, index) {
        return _buildTreeNode(_treeNodes[index]);
      },
    );
  }

  /// 构建树节点
  Widget _buildTreeNode(EmployeeTreeNode node) {
    if (!node.isVisible) return const SizedBox.shrink();

    return Column(
      children: [
        _buildNodeItem(node),
        if (node.isExpanded && node.children.isNotEmpty)
          ...node.children.map(
            (child) =>
                Padding(padding: const EdgeInsets.only(left: 20), child: _buildTreeNode(child)),
          ),
      ],
    );
  }

    /// 构建展开/折叠图标
  Widget _buildExpandIcon(EmployeeTreeNode node) {
    return SizedBox(
      width: DepartmentTreeStyles.expandIconSize.width,
      height: DepartmentTreeStyles.expandIconSize.height,
      child:
          node.children.isNotEmpty
              ? _ExpandIcon(isExpanded: node.isExpanded, onTap: () => _handleNodeToggle(node))
              : null, // 叶子节点不显示图标，但保持占位空间
    );
  }


  /// 构建节点项
  Widget _buildNodeItem(EmployeeTreeNode node) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: Row(
        children: [
          // 展开/收起图标
          if (node.children.isNotEmpty)
            GestureDetector(
              onTap: () {
                setState(() {
                  node.isExpanded = !node.isExpanded;
                });
              },
              child: Icon(node.isExpanded ? Icons.expand_more : Icons.chevron_right, size: 20),
            )
          else
            const SizedBox(width: 20),

          // 复选框
          if (widget.showCheckbox)
            Checkbox(
              value: node.isIndeterminate ? null : node.isChecked,
              tristate: true,
              onChanged: (value) {
                // 当点击半选状态的复选框时，设置为选中状态
                if (node.isIndeterminate) {
                  _setNodeChecked(node, true);
                } else {
                  _setNodeChecked(node, value ?? false);
                }
              },
            ),

          // 头像和图标
          if (node.employee.isEmployee) ...[
            Icon(Icons.person, size: 16, color: Colors.blue),
            const SizedBox(width: 8),
          ],

          // 名称
          Expanded(
            child: GestureDetector(
              onTap: () {
                widget.onNodeTap?.call(node.employee);
              },
              child: Text(node.employee.name),
            ),
          ),
        ],
      ),
    );
  }
}
