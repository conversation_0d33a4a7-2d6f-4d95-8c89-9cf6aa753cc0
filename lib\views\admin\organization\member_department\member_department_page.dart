import 'package:flutter/material.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/components/selector/department_selector/department_tree.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'package:octasync_client/views/admin/organization/member_department/create_department_dialog.dart';

class MemberDepartmentPage extends StatefulWidget {
  const MemberDepartmentPage({super.key});

  @override
  State<MemberDepartmentPage> createState() => _MemberDepartmentPageState();
}

class _MemberDepartmentPageState extends State<MemberDepartmentPage> {
  // 创建 GlobalKey 用于访问 DepartmentSelector 的方法
  final GlobalKey<DepartmentTreeState> _departmentSelectorKey = GlobalKey<DepartmentTreeState>();

  // 当前选中的部门（高亮显示）
  DepartmentModel? _selectedDepartment;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // 左侧部门选择器
        Container(
          width: 250,
          decoration: BoxDecoration(border: Border(right: BorderSide(color: context.border300))),
          child: DepartmentTree(
            key: _departmentSelectorKey,
            onNodeTap: (department) {
              // 处理部门节点点击事件（高亮显示）
              setState(() {
                _selectedDepartment = department;
              });
              debugPrint('高亮选择了部门: ${department.departmentName}');
            },
          ),
        ),

        // 右侧操作区域
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 顶部操作栏
                Row(
                  children: [
                    CreateDepartmentDrawer(
                      onSuccess: () {
                        _departmentSelectorKey.currentState?.refresh();
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
